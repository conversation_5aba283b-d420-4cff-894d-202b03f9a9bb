# 擎路AI产品开发全流程最佳实践

## 📋 文档概述

本文档基于H5订单详情取消时间字段项目的成功实践，总结了从需求分析到开发实施的完整流程，为团队提供标准化的产品开发最佳实践。

## 🎯 核心理念

### AURA协议驱动
采用**自适应、统一、响应式**的AI协作模式：
- **自适应性**: 根据任务复杂度选择合适的执行策略
- **上下文感知**: 深度理解项目架构和技术栈
- **效率优先**: 减少不必要的交互，提高开发效率
- **质量保证**: 确保交付物的完整性和准确性

### 全流程标准化
建立从需求到开发的标准化流程，确保：
- 需求理解的准确性
- 产品设计的完整性
- 技术实现的可行性
- 开发交付的高效性

## 🔄 完整开发流程

### 阶段一：需求分析与理解 (Requirements Analysis)

#### 1.1 需求输入标准格式
```markdown
需求：[具体功能描述]
场景：[业务使用场景]
参考：[现有系统页面或功能]
平台：[H5/PC端]
优先级：[高/中/低]
技术要求：[特殊技术限制，可选]
```

#### 1.2 需求分析检查清单
- [ ] **功能边界清晰**: 明确功能范围和限制
- [ ] **用户场景明确**: 理解用户使用场景和痛点
- [ ] **技术可行性**: 评估技术实现的可行性
- [ ] **业务价值**: 确认功能的业务价值和优先级
- [ ] **依赖关系**: 识别与其他功能的依赖关系

#### 1.3 系统调研方法
```bash
# 1. 项目架构分析
查看: modules/{h5|pc}/project-doc/项目架构文档.md
理解: 技术栈、组件库、数据流

# 2. 路由配置分析  
查看: modules/{h5|pc}/router-file/路由.md
理解: 页面结构、导航关系

# 3. 现有组件分析
查看: modules/{h5|pc}/comp-lib/
理解: 可复用组件、样式规范

# 4. 线上系统调研
访问: http://dolphin.qinglusaas-dev.com (H5)
访问: http://whale.qinglusaas-dev.com (PC)
使用: modules/auth.json 进行认证
```

### 阶段二：产品设计与原型 (Product Design)

#### 2.1 原型设计原则
- **像素级复刻**: 100%还原线上系统的视觉效果
- **功能完整性**: 包含所有交互状态和动画效果
- **改动标注**: 明确标注新增和修改的功能区域
- **响应式设计**: 适配不同屏幕尺寸和设备

#### 2.2 原型制作流程
```html
<!-- 标准HTML原型结构 -->
<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>[功能名称] - 擎路[H5|PC]</title>
  
  <!-- 引入线上系统资源 -->
  <link rel="stylesheet" href="[线上CSS]">
  <script src="[线上JS]"></script>
  
  <style>
    /* 像素级复刻样式 */
    /* 改动点标注样式 */
    .change-highlight {
      border: 2px dashed #ff4d4f !important;
      background-color: rgba(255, 77, 79, 0.1) !important;
    }
    .change-highlight::before {
      content: "NEW";
      background: #ff4d4f;
      color: white;
      /* 标注样式 */
    }
  </style>
</head>
<body>
  <!-- 完整页面结构 -->
  <!-- Mock数据和交互逻辑 -->
</body>
</html>
```

#### 2.3 设计质量检查
- [ ] **视觉一致性**: 与线上系统100%视觉一致
- [ ] **交互完整性**: 包含所有交互状态
- [ ] **功能标注**: 新增功能正确标注
- [ ] **数据合理性**: Mock数据真实可信
- [ ] **响应式适配**: 多设备兼容

### 阶段三：技术文档编写 (Technical Documentation)

#### 3.1 标准文档体系
```
prd/v[版本号]/[平台]/
├── [功能名称]-原型图-v[版本号].html          # 必需
├── [功能名称]-PRD-v[版本号].md               # 产品需求文档
├── [功能名称]-技术变更说明-v[版本号].md       # 技术实现指南
├── [功能名称]-数据模型变更说明-v[版本号].md   # 数据模型变更
├── [功能名称]-UI组件变更方案-v[版本号].md     # UI组件设计
├── [功能名称]-接口规范文档-v[版本号].md       # API接口规范
├── [功能名称]-组件使用指南-v[版本号].md       # 开发指南
├── [功能名称]-组件对照表-v[版本号].md         # 组件映射
└── [功能名称]-开发提示词-v[版本号].md         # 开发实施
```

#### 3.2 文档编写标准
```markdown
# 标准文档头部
## 📋 文档信息
| 项目 | 内容 |
|------|------|
| **文档标题** | [功能名称] - [文档类型] |
| **版本号** | v[版本号] |
| **平台** | [H5移动端/PC端] |
| **创建日期** | YYYY-MM-DD |
| **文档类型** | [文档类型] |

## 🎯 变更概述
### 核心变更
[简要描述核心变更内容]

### 变更范围
[详细说明变更影响范围]
```

#### 3.3 技术深度要求
- **数据模型**: 详细的实体类和字段变更
- **UI组件**: 完整的组件设计和样式规范
- **API接口**: 标准的接口规范和数据结构
- **实现指南**: 具体的代码示例和实施步骤

### 阶段四：开发提示词生成 (Development Prompt)

#### 4.1 开发提示词结构
```markdown
# [功能名称] - 开发提示词 v[版本号]

## 🚀 快速开始
### 开发环境要求
### 核心文件清单

## 🔧 开发实施步骤
### 第一步：数据模型扩展
### 第二步：工具函数扩展  
### 第三步：UI组件实现
### 第四步：API接口调整

## 🧪 测试指南
### 单元测试
### 集成测试场景

## ⚠️ 注意事项
### 开发注意事项
### 部署注意事项
### 测试重点
```

#### 4.2 代码示例标准
```javascript
// 🆕 新增功能标注
// 文件路径注释
export class ExampleClass {
  constructor(data = {}) {
    // 现有字段保持不变
    this.existingField = data.existingField
    
    // 🆕 新增：新功能字段
    this.newField = data.newField || null
  }
  
  // 🆕 新增：新功能方法
  newMethod() {
    // 实现逻辑
  }
}
```

## 📊 质量保证体系

### 文档质量检查清单

#### 原型视觉稿检查
- [ ] **像素级复刻**: 与线上系统100%视觉一致
- [ ] **功能完整**: 所有功能区域和交互元素完整
- [ ] **改动标注**: 新增和修改功能正确标注
- [ ] **响应式**: 支持不同屏幕尺寸
- [ ] **Mock数据**: 使用合理的模拟数据

#### 技术文档检查
- [ ] **结构规范**: 按照标准模板编写
- [ ] **内容完整**: 涵盖所有技术要点
- [ ] **代码准确**: 代码示例可直接使用
- [ ] **版本一致**: 版本号和命名规范一致
- [ ] **交叉引用**: 文档间引用关系正确

#### 开发提示词检查
- [ ] **步骤清晰**: 开发步骤逻辑清晰
- [ ] **代码完整**: 包含完整的代码示例
- [ ] **测试覆盖**: 包含测试用例和场景
- [ ] **注意事项**: 涵盖开发、部署、测试要点
- [ ] **资源链接**: 相关资源链接正确

### 技术实现检查清单

#### 前端实现
- [ ] **组件设计**: Vue组件结构合理
- [ ] **样式规范**: 遵循项目样式规范
- [ ] **响应式**: 移动端适配完整
- [ ] **交互逻辑**: 用户交互流畅
- [ ] **性能优化**: 避免性能瓶颈

#### 后端实现
- [ ] **数据模型**: 数据库设计合理
- [ ] **API设计**: 接口设计RESTful
- [ ] **数据验证**: 输入输出验证完整
- [ ] **错误处理**: 异常处理机制完善
- [ ] **性能考虑**: 查询性能优化

## 🛠️ 工具和资源

### 必需工具
- **Playwright**: 线上系统访问和分析
- **Vue DevTools**: 前端组件调试
- **Chrome DevTools**: 样式分析和性能调试
- **Postman**: API接口测试

### 参考资源
- **项目架构文档**: `modules/{h5|pc}/project-doc/`
- **组件库文档**: `modules/{h5|pc}/comp-lib/`
- **全局规则**: `.cursor/rules/global.md`
- **认证配置**: `modules/auth.json`

### 线上系统
- **H5端**: http://dolphin.qinglusaas-dev.com
- **PC端**: http://whale.qinglusaas-dev.com

## 📈 持续改进

### 流程优化
1. **反馈收集**: 收集开发团队对流程的反馈
2. **效率分析**: 分析各阶段的时间消耗和效率
3. **质量评估**: 评估交付物的质量和准确性
4. **流程迭代**: 根据反馈持续优化流程

### 最佳实践更新
1. **技术栈更新**: 跟随项目技术栈的演进
2. **工具升级**: 采用更高效的开发工具
3. **规范完善**: 完善文档和代码规范
4. **模板优化**: 优化文档和代码模板

## 🎯 成功案例

### H5订单详情取消时间字段项目
- **需求**: 在H5订单详情页面新增取消时间字段显示
- **交付物**: 9个完整的技术文档 + 像素级HTML原型
- **技术栈**: Vue 3 + Vant 3 + SCSS + DataX
- **开发周期**: 预估2-3个工作日
- **质量指标**: 100%像素级复刻，完整技术方案

### 关键成功因素
1. **需求理解准确**: 通过线上系统调研准确理解需求
2. **技术方案完整**: 涵盖前端、后端、数据库的完整方案
3. **文档体系标准**: 按照标准模板生成完整文档体系
4. **代码示例实用**: 提供可直接使用的代码示例
5. **质量保证严格**: 多层次的质量检查确保交付质量

## ⚠️ 注意事项

### 流程执行要点
1. **严格按流程**: 不跳过任何关键步骤
2. **质量优先**: 质量比速度更重要
3. **文档标准**: 严格按照模板和规范编写
4. **代码准确**: 确保代码示例的准确性和可用性
5. **持续改进**: 根据实践经验持续优化流程

### 常见问题避免
1. **需求理解偏差**: 充分调研现有系统
2. **技术方案不完整**: 考虑前后端完整技术栈
3. **文档质量不高**: 使用标准模板和检查清单
4. **代码示例错误**: 验证代码的正确性
5. **版本管理混乱**: 严格按照版本号规范

## 🚀 快速实施指南

### 新项目启动清单
```bash
# 1. 环境准备
□ 确认技术栈版本 (Vue 3, Vant 3, etc.)
□ 配置开发环境和工具
□ 获取线上系统访问权限
□ 准备认证配置文件

# 2. 需求分析
□ 收集完整需求信息
□ 分析现有系统架构
□ 确定技术实现方案
□ 评估开发工作量

# 3. 产品设计
□ 创建像素级HTML原型
□ 标注功能改动点
□ 验证响应式设计
□ 准备Mock测试数据

# 4. 技术文档
□ 编写PRD产品需求文档
□ 编写技术变更说明
□ 编写接口规范文档
□ 编写开发提示词

# 5. 质量检查
□ 原型视觉稿质量检查
□ 技术文档完整性检查
□ 代码示例准确性验证
□ 交叉引用关系检查
```

### 团队协作模式

#### 角色分工
| 角色 | 职责 | 交付物 |
|------|------|--------|
| **产品经理** | 需求分析、原型设计 | 原型图、PRD文档 |
| **技术负责人** | 技术方案设计 | 技术变更说明、接口规范 |
| **前端开发** | UI组件实现 | 组件代码、样式文件 |
| **后端开发** | API接口开发 | 接口实现、数据模型 |
| **测试工程师** | 质量保证 | 测试用例、测试报告 |

#### 协作流程
```mermaid
graph TD
    A[需求输入] --> B[产品经理分析]
    B --> C[技术负责人评估]
    C --> D[原型设计]
    D --> E[技术文档编写]
    E --> F[开发实施]
    F --> G[测试验证]
    G --> H[上线部署]

    style D fill:#fff7e6,stroke:#ffa940
    style E fill:#fff7e6,stroke:#ffa940
```

## 📚 模板库

### 需求输入模板
```markdown
# 功能需求模板

## 基本信息
- **需求标题**: [功能名称]
- **需求类型**: [新增功能/功能优化/Bug修复]
- **优先级**: [P0/P1/P2/P3]
- **预期完成时间**: [YYYY-MM-DD]

## 需求描述
### 业务背景
[描述业务背景和问题]

### 功能需求
[详细描述功能需求]

### 用户场景
[描述用户使用场景]

### 验收标准
- [ ] [验收条件1]
- [ ] [验收条件2]
- [ ] [验收条件3]

## 技术要求
### 平台要求
- [ ] H5移动端
- [ ] PC端

### 性能要求
[性能指标要求]

### 兼容性要求
[浏览器和设备兼容性]

## 参考资料
- **现有功能**: [相关页面路径]
- **设计稿**: [设计稿链接]
- **竞品参考**: [竞品功能说明]
```

### 技术评估模板
```markdown
# 技术评估模板

## 技术可行性
### 前端技术栈
- **框架**: Vue 3 + Composition API
- **UI库**: Vant 3 (H5) / Element Plus (PC)
- **样式**: SCSS + CSS Modules
- **状态管理**: Pinia
- **路由**: Vue Router 4

### 后端技术栈
- **框架**: [后端框架]
- **数据库**: [数据库类型]
- **缓存**: [缓存方案]
- **API**: RESTful / GraphQL

## 实现方案
### 数据模型设计
[数据库表结构设计]

### API接口设计
[接口设计方案]

### 前端组件设计
[组件架构设计]

## 工作量评估
| 模块 | 工作量(人天) | 负责人 |
|------|-------------|--------|
| 数据模型 | X | 后端开发 |
| API接口 | X | 后端开发 |
| 前端组件 | X | 前端开发 |
| 测试验证 | X | 测试工程师 |
| **总计** | **X** | - |

## 风险评估
### 技术风险
- [风险点1]: [风险描述] - [应对方案]
- [风险点2]: [风险描述] - [应对方案]

### 时间风险
- [风险点1]: [风险描述] - [应对方案]
- [风险点2]: [风险描述] - [应对方案]
```

## 🔍 常见问题解答

### Q1: 如何确保原型的像素级复刻？
**A**:
1. 使用Playwright访问线上系统获取真实样式
2. 通过Chrome DevTools精确测量尺寸和颜色
3. 直接引用线上系统的CSS和JS资源
4. 使用相同的组件库和设计规范

### Q2: 技术文档应该写到什么程度？
**A**:
1. **代码示例**: 提供可直接使用的代码片段
2. **实现步骤**: 详细的分步实施指南
3. **注意事项**: 开发、测试、部署的关键点
4. **质量标准**: 明确的验收标准和检查清单

### Q3: 如何处理需求变更？
**A**:
1. **影响评估**: 评估变更对现有方案的影响
2. **方案调整**: 更新相关技术文档和原型
3. **版本管理**: 使用新版本号管理变更
4. **团队同步**: 及时同步变更信息给相关人员

### Q4: 如何保证文档质量？
**A**:
1. **模板标准**: 严格按照标准模板编写
2. **交叉检查**: 多人review文档内容
3. **实际验证**: 验证代码示例的正确性
4. **持续更新**: 根据实施反馈持续优化

### Q5: 开发过程中遇到技术难点怎么办？
**A**:
1. **技术调研**: 深入研究技术实现方案
2. **原型验证**: 通过小范围原型验证可行性
3. **专家咨询**: 寻求技术专家的建议
4. **方案调整**: 必要时调整技术方案

## 📈 效果评估

### 量化指标
| 指标 | 目标值 | 当前值 | 说明 |
|------|--------|--------|------|
| **需求理解准确率** | ≥95% | - | 需求变更次数/总需求数 |
| **原型复刻准确度** | ≥98% | - | 视觉还原度评分 |
| **文档完整性** | 100% | - | 必需文档覆盖率 |
| **代码示例可用性** | ≥95% | - | 可直接使用的代码比例 |
| **开发效率提升** | ≥30% | - | 相比传统流程的效率提升 |

### 质量评估
1. **产品质量**: 功能完整性、用户体验
2. **技术质量**: 代码规范、性能表现
3. **文档质量**: 完整性、准确性、可读性
4. **流程效率**: 开发周期、沟通成本

### 持续改进机制
1. **定期回顾**: 每月进行流程回顾和优化
2. **反馈收集**: 收集团队成员的改进建议
3. **最佳实践更新**: 及时更新最佳实践文档
4. **工具升级**: 采用更高效的开发工具和方法

---

**总结**: 本最佳实践基于H5订单详情取消时间字段项目的成功经验总结，为团队提供了从需求到开发的完整标准化流程。通过严格执行此流程，可以确保产品开发的高效性和高质量，实现需求理解准确率≥95%、原型复刻准确度≥98%、开发效率提升≥30%的目标。
