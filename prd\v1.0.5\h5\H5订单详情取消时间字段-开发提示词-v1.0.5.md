# H5订单详情取消时间字段 - 开发提示词 v1.0.5

## 📋 文档信息

| 项目 | 内容 |
|------|------|
| **文档标题** | H5订单详情取消时间字段 - 开发提示词 |
| **版本号** | v1.0.5 |
| **平台** | H5移动端 |
| **创建日期** | 2025-07-30 |
| **文档类型** | 开发提示词 |

## 🎯 功能概述

### 核心需求
在H5订单详情页面的订单基本信息区域新增`cancelTime`字段显示，为已取消订单提供取消时间信息，提升用户对订单状态的理解。

### 关键特性
- **条件显示**: 仅在订单状态为"已取消"且存在取消时间时显示
- **视觉突出**: 使用橙色背景和边框，配合"NEW"标识突出新功能
- **时间格式**: 显示格式为"取消：YYYY-MM-DD HH:mm"
- **响应式设计**: 适配不同移动设备屏幕尺寸

## 🚀 快速开始

### 开发环境要求
```bash
# 技术栈
Vue 3 + Composition API
Vant 3 UI组件库
SCSS样式预处理器
DataX数据建模框架
Tempo时间工具类

# 开发工具
Node.js >= 16.0.0
npm >= 8.0.0
```

### 核心文件清单
```
📁 需要修改的文件
├── modules/h5/comp-lib/src/services/entities/OrderEntity.js
├── modules/h5/comp-lib/src/services/models/OrderDetailModels/OrderDetailInfoModel.js
├── modules/h5/comp-lib/src/utils/tempo.js
├── views/order/OrderDetail.vue
└── api/order.js (后端接口)

📁 参考文档
├── prd/v1.0.5/h5/H5订单详情取消时间字段-原型图-v1.0.5.html
├── prd/v1.0.5/h5/H5订单详情取消时间字段-PRD-v1.0.5.md
├── prd/v1.0.5/h5/H5订单详情取消时间字段-技术变更说明-v1.0.5.md
├── prd/v1.0.5/h5/H5订单详情取消时间字段-数据模型变更说明-v1.0.5.md
├── prd/v1.0.5/h5/H5订单详情取消时间字段-UI组件变更方案-v1.0.5.md
├── prd/v1.0.5/h5/H5订单详情取消时间字段-接口规范文档-v1.0.5.md
├── prd/v1.0.5/h5/H5订单详情取消时间字段-组件使用指南-v1.0.5.md
└── prd/v1.0.5/h5/H5订单详情取消时间字段-组件对照表-v1.0.5.md
```

## 🔧 开发实施步骤

### 第一步：数据模型扩展

#### 1.1 扩展OrderEntity实体
```javascript
// 文件：modules/h5/comp-lib/src/services/entities/OrderEntity.js
export class OrderEntity extends DataX {
  constructor(data = {}) {
    super()
    // 现有字段...
    
    // 🆕 新增：取消时间字段
    this.cancelTime = data.cancelTime || null
  }
  
  // 🆕 新增：判断是否为已取消订单
  isCancelled() {
    return this.status === 'CANCELLED'
  }
  
  // 🆕 新增：获取取消时间显示文本
  getCancelTimeText() {
    if (!this.isCancelled() || !this.cancelTime) return ''
    return `取消：${this.formatTime(this.cancelTime)}`
  }
}
```

#### 1.2 扩展OrderDetailInfoModel数据模型
```javascript
// 文件：modules/h5/comp-lib/src/services/models/OrderDetailModels/OrderDetailInfoModel.js
export class OrderDetailInfoModel extends OrderBaseInfoModel {
  constructor(data = {}) {
    super(data)
    
    // 🆕 新增：取消时间相关字段
    this.cancelTime = data.cancelTime || null
    this.cancelTimeStr = this.formatCancelTime(data.cancelTime)
  }
  
  // 🆕 新增：格式化取消时间方法
  formatCancelTime(cancelTime) {
    if (!cancelTime) return ''
    return Tempo.format(cancelTime, 'YYYY-MM-DD HH:mm')
  }
  
  // 🆕 新增：判断是否显示取消时间
  shouldShowCancelTime() {
    return this.status === 'CANCELLED' && this.cancelTime
  }
  
  // 🆕 新增：获取取消时间显示文本
  getCancelTimeDisplay() {
    if (!this.shouldShowCancelTime()) return ''
    return `取消：${this.cancelTimeStr}`
  }
}
```

### 第二步：工具函数扩展

#### 2.1 扩展Tempo时间工具类
```javascript
// 文件：modules/h5/comp-lib/src/utils/tempo.js
export class Tempo {
  // 🆕 新增：专门用于取消时间格式化
  static formatCancelTime(cancelTime) {
    if (!cancelTime) return ''
    
    try {
      const date = new Date(cancelTime)
      if (isNaN(date.getTime())) {
        console.warn('Invalid cancel time:', cancelTime)
        return ''
      }
      
      return this.format(cancelTime, 'YYYY-MM-DD HH:mm')
    } catch (error) {
      console.error('Error formatting cancel time:', error)
      return ''
    }
  }
  
  // 🆕 新增：获取相对时间描述
  static getRelativeCancelTime(cancelTime) {
    if (!cancelTime) return ''
    
    const now = new Date()
    const cancel = new Date(cancelTime)
    const diffMs = now.getTime() - cancel.getTime()
    const diffHours = Math.floor(diffMs / (1000 * 60 * 60))
    const diffDays = Math.floor(diffHours / 24)
    
    if (diffDays > 0) {
      return `${diffDays}天前取消`
    } else if (diffHours > 0) {
      return `${diffHours}小时前取消`
    } else {
      return '刚刚取消'
    }
  }
}
```

### 第三步：UI组件实现

#### 3.1 Vue组件模板
```vue
<!-- 文件：views/order/OrderDetail.vue -->
<template>
  <div class="order-detail">
    <van-cell-group>
      <van-cell>
        <div class="order-info">
          <!-- 下单时间 -->
          <div class="info-item">
            下单：{{ orderDetail.orderTimeStr }}
            <span class="creator">创建人：{{ orderDetail.creatorName }}</span>
          </div>
          
          <!-- 用车时间 -->
          <div class="info-item">
            用车：{{ orderDetail.startTimeStr }} 至 {{ orderDetail.endTimeStr }}, {{ orderDetail.durationStr }}
          </div>
          
          <!-- 🆕 新增：取消时间字段 -->
          <div v-if="shouldShowCancelTime" class="info-item cancel-time-item">
            <span class="cancel-time-text">
              {{ getCancelTimeDisplay() }}
            </span>
          </div>
          
          <!-- 订单号 -->
          <div class="info-item">
            <span>订单号：{{ orderDetail.orderNo }}</span>
            <van-button size="mini" @click="copyOrderNo">复制</van-button>
          </div>
        </div>
      </van-cell>
    </van-cell-group>
  </div>
</template>
```

#### 3.2 Vue组件逻辑
```javascript
<script>
import { OrderDetailInfoModel } from '@/models/OrderDetailInfoModel'

export default {
  name: 'OrderDetail',
  data() {
    return {
      orderDetail: new OrderDetailInfoModel()
    }
  },
  computed: {
    // 🆕 新增：判断是否显示取消时间
    shouldShowCancelTime() {
      return this.orderDetail.status === 'CANCELLED' && 
             this.orderDetail.cancelTime
    }
  },
  methods: {
    // 🆕 新增：获取取消时间显示文本
    getCancelTimeDisplay() {
      if (!this.shouldShowCancelTime) return ''
      return this.orderDetail.getCancelTimeDisplay()
    }
  }
}
</script>
```

#### 3.3 样式实现
```scss
<style lang="scss" scoped>
// 🆕 新增：取消时间字段样式
.cancel-time-item {
  background: #fff7e6;
  border: 2px solid #ffa940;
  border-radius: 6px;
  padding: 8px;
  margin: 8px 0;
  position: relative;
  
  // NEW功能标识
  &::before {
    content: "NEW";
    position: absolute;
    top: -8px;
    right: 8px;
    background: #ff4d4f;
    color: white;
    padding: 2px 6px;
    border-radius: 4px;
    font-size: 10px;
    font-weight: bold;
    z-index: 1;
  }
}

.cancel-time-text {
  color: #d4380d;
  font-weight: 500;
  font-size: 14px;
  line-height: 1.4;
}

// 响应式适配
@media (max-width: 320px) {
  .cancel-time-item {
    padding: 6px;
    margin: 6px 0;
    
    &::before {
      font-size: 9px;
      padding: 1px 4px;
    }
  }
  
  .cancel-time-text {
    font-size: 13px;
  }
}
</style>
```

### 第四步：API接口调整

#### 4.1 订单详情接口响应
```javascript
// 接口路径：/api/h5/order/detail
// 响应数据结构调整
{
  "code": 200,
  "message": "success",
  "data": {
    "id": 1705023,
    "orderNo": "1705023",
    "status": "CANCELLED",
    "orderTime": "2025-05-19T11:30:00Z",
    "startTime": "2025-05-19T12:00:00Z",
    "endTime": "2025-05-20T12:00:00Z",
    "createTime": "2025-05-19T11:30:00Z",
    "updateTime": "2025-05-19T11:45:00Z",
    
    // 🆕 新增字段
    "cancelTime": "2025-05-19T11:45:00Z",
    
    // 其他现有字段...
  }
}
```

#### 4.2 数据验证逻辑
```javascript
// 前端数据验证
const validateOrderData = (orderData) => {
  const errors = []
  
  // 验证取消时间与订单状态的一致性
  if (orderData.status === 'CANCELLED') {
    if (!orderData.cancelTime) {
      errors.push('已取消订单必须包含取消时间')
    }
  } else if (orderData.cancelTime) {
    errors.push('非取消订单不应包含取消时间')
  }
  
  return errors
}
```

## 🧪 测试指南

### 单元测试
```javascript
// 组件渲染测试
describe('OrderDetail Cancel Time', () => {
  test('显示已取消订单的取消时间', () => {
    const wrapper = mount(OrderDetail, {
      data() {
        return {
          orderDetail: {
            status: 'CANCELLED',
            cancelTime: '2025-05-19T11:45:00Z'
          }
        }
      }
    })
    
    expect(wrapper.find('.cancel-time-item').exists()).toBe(true)
    expect(wrapper.find('.cancel-time-text').text()).toContain('取消：')
  })
  
  test('不显示非取消订单的取消时间', () => {
    const wrapper = mount(OrderDetail, {
      data() {
        return {
          orderDetail: {
            status: 'CONFIRMED',
            cancelTime: null
          }
        }
      }
    })
    
    expect(wrapper.find('.cancel-time-item').exists()).toBe(false)
  })
})
```

### 集成测试场景
1. **已取消订单**: 验证取消时间正确显示
2. **正常订单**: 验证取消时间不显示
3. **数据异常**: 验证错误处理和优雅降级
4. **响应式**: 验证不同设备尺寸下的显示效果

## 📱 设备兼容性

### 支持设备
- **iPhone**: iPhone 6及以上版本
- **Android**: Android 5.0及以上版本
- **屏幕尺寸**: 320px - 414px宽度

### 浏览器兼容
- **Safari**: iOS 10+
- **Chrome**: Android 5.0+
- **微信浏览器**: 最新版本
- **支付宝浏览器**: 最新版本

## ⚠️ 注意事项

### 开发注意事项
1. **数据一致性**: 确保取消时间与订单状态的一致性
2. **时区处理**: 正确处理UTC时间到本地时间的转换
3. **错误处理**: 优雅处理数据缺失和格式错误
4. **性能优化**: 使用计算属性缓存避免重复计算

### 部署注意事项
1. **数据库变更**: 先执行数据库字段新增
2. **API更新**: 确保后端接口返回cancelTime字段
3. **历史数据**: 处理历史取消订单的数据迁移
4. **版本兼容**: 确保新版本向后兼容

### 测试重点
1. **功能测试**: 验证取消时间显示逻辑
2. **UI测试**: 验证样式和响应式效果
3. **兼容性测试**: 验证不同设备和浏览器
4. **性能测试**: 验证页面加载和渲染性能

## 🔗 相关资源

### 设计资源
- **原型图**: `H5订单详情取消时间字段-原型图-v1.0.5.html`
- **UI规范**: 橙色主题 (#fff7e6背景, #ffa940边框, #d4380d文字)

### 技术文档
- **PRD文档**: 完整的产品需求说明
- **技术变更说明**: 详细的实现指南
- **接口规范**: API接口详细说明
- **组件对照表**: 组件使用映射关系

### 开发工具
- **Vue DevTools**: 组件调试工具
- **Chrome DevTools**: 样式调试和性能分析
- **Vant文档**: https://vant-contrib.gitee.io/vant/v3/

## 📈 后续优化

### 功能扩展
1. **相对时间显示**: "2小时前取消"等相对时间描述
2. **取消原因**: 显示订单取消的具体原因
3. **操作记录**: 完整的订单状态变更历史

### 性能优化
1. **懒加载**: 非关键信息的懒加载
2. **缓存策略**: 订单详情数据缓存
3. **预加载**: 相关订单信息预加载

### 用户体验
1. **动画效果**: 取消时间字段的入场动画
2. **交互反馈**: 点击复制等操作的反馈
3. **无障碍**: 屏幕阅读器支持和键盘导航

---

**开发提示**: 本功能采用渐进式开发策略，优先实现核心显示功能，后续可根据用户反馈进行功能扩展和体验优化。
