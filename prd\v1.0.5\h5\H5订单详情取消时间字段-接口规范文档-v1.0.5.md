# H5订单详情取消时间字段 - 接口规范文档 v1.0.5

## 📋 文档信息

| 项目 | 内容 |
|------|------|
| **文档标题** | H5订单详情取消时间字段 - 接口规范文档 |
| **版本号** | v1.0.5 |
| **平台** | H5移动端 |
| **创建日期** | 2025-07-30 |
| **文档类型** | 接口规范文档 |

## 🎯 变更概述

### 核心变更
在订单详情接口中新增`cancelTime`字段，为前端提供订单取消时间数据支持。

### 变更范围
- **订单详情接口** 响应数据结构扩展
- **数据库表结构** 新增取消时间字段
- **业务逻辑** 订单取消流程调整
- **数据验证** 新增字段验证规则

## 🔌 接口详细规范

### 1. 订单详情查询接口

#### 1.1 基本信息
| 项目 | 内容 |
|------|------|
| **接口名称** | 订单详情查询 |
| **接口路径** | `/api/h5/order/detail` |
| **请求方法** | GET |
| **接口版本** | v1.0.5 |
| **认证方式** | Bearer Token |

#### 1.2 请求参数
```json
{
  "orderId": "1705023"
}
```

| 参数名 | 类型 | 必填 | 说明 | 示例值 |
|--------|------|------|------|--------|
| `orderId` | String | 是 | 订单ID | "1705023" |

#### 1.3 响应数据结构
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "id": 1705023,
    "orderNo": "1705023",
    "status": "CANCELLED",
    "orderTime": "2025-05-19T11:30:00Z",
    "startTime": "2025-05-19T12:00:00Z",
    "endTime": "2025-05-20T12:00:00Z",
    "createTime": "2025-05-19T11:30:00Z",
    "updateTime": "2025-05-19T11:45:00Z",
    
    // 🆕 新增字段
    "cancelTime": "2025-05-19T11:45:00Z",
    
    // 订单创建人信息
    "creatorInfo": {
      "id": "admin001",
      "name": "admin",
      "role": "ADMIN"
    },
    
    // 车辆信息
    "vehicleInfo": {
      "id": 1376,
      "brand": "大众",
      "model": "迈腾GTE插电混动",
      "year": "2020款",
      "trim": "GTE 豪华型",
      "plateNo": "琼BF09400",
      "category": "舒适型"
    },
    
    // 客户信息
    "customerInfo": {
      "name": "admin",
      "phone": "13627003150",
      "idCard": "1231321313213",
      "idCardType": "身份证"
    },
    
    // 价格信息
    "priceInfo": {
      "totalAmount": 8.00,
      "paidAmount": 8.00,
      "deposit": 5000.00,
      "carDeposit": 3000.00,
      "violationDeposit": 2000.00
    },
    
    // 门店信息
    "storeInfo": {
      "pickupStore": {
        "id": "store_001",
        "name": "神风租车22",
        "address": "云南普洱梅子湖公园公园分店",
        "type": "到店取车"
      },
      "returnStore": {
        "id": "store_001", 
        "name": "神风租车22",
        "address": "云南普洱梅子湖公园公园分店",
        "type": "到店还车"
      }
    },
    
    // 服务信息
    "serviceInfo": {
      "insurance": [
        {
          "name": "基本保障服务费",
          "status": "部分退款",
          "price": 1.00
        }
      ],
      "additionalServices": [
        {
          "name": "手续费",
          "status": "部分退款", 
          "price": 1.00
        },
        {
          "name": "儿童座椅",
          "status": "部分退款",
          "price": 5.00
        }
      ]
    },
    
    // 标签信息
    "tags": ["押金未收", "手续费", "儿童座椅"]
  }
}
```

#### 1.4 新增字段详细说明

| 字段名 | 类型 | 必填 | 说明 | 约束条件 |
|--------|------|------|------|----------|
| `cancelTime` | String | 否 | 订单取消时间 | ISO 8601格式，仅取消订单返回 |

**字段约束**:
- **数据格式**: ISO 8601 时间戳格式 (YYYY-MM-DDTHH:mm:ssZ)
- **时区**: UTC时区，前端负责本地化显示
- **空值处理**: 非取消订单该字段为null或不返回
- **数据来源**: 订单状态变更为CANCELLED时的时间戳

#### 1.5 响应状态码

| 状态码 | 说明 | 响应示例 |
|--------|------|----------|
| 200 | 请求成功 | 正常返回订单详情数据 |
| 400 | 请求参数错误 | `{"code": 400, "message": "订单ID不能为空"}` |
| 401 | 未授权访问 | `{"code": 401, "message": "请先登录"}` |
| 403 | 权限不足 | `{"code": 403, "message": "无权限查看该订单"}` |
| 404 | 订单不存在 | `{"code": 404, "message": "订单不存在"}` |
| 500 | 服务器内部错误 | `{"code": 500, "message": "服务器内部错误"}` |

### 2. 订单取消接口调整

#### 2.1 基本信息
| 项目 | 内容 |
|------|------|
| **接口名称** | 订单取消 |
| **接口路径** | `/api/h5/order/cancel` |
| **请求方法** | POST |
| **接口版本** | v1.0.5 |
| **认证方式** | Bearer Token |

#### 2.2 请求参数
```json
{
  "orderId": "1705023",
  "cancelReason": "用户主动取消",
  "cancelNote": "客户临时有事，无法按时用车"
}
```

| 参数名 | 类型 | 必填 | 说明 | 示例值 |
|--------|------|------|------|--------|
| `orderId` | String | 是 | 订单ID | "1705023" |
| `cancelReason` | String | 是 | 取消原因 | "用户主动取消" |
| `cancelNote` | String | 否 | 取消备注 | "客户临时有事" |

#### 2.3 响应数据结构
```json
{
  "code": 200,
  "message": "订单取消成功",
  "data": {
    "orderId": "1705023",
    "status": "CANCELLED",
    "cancelTime": "2025-05-19T11:45:00Z",
    "cancelReason": "用户主动取消",
    "refundInfo": {
      "refundAmount": 6.00,
      "refundTime": "2025-05-19T11:45:00Z",
      "refundMethod": "原路退回"
    }
  }
}
```

#### 2.4 业务逻辑调整
```javascript
// 订单取消业务逻辑
async function cancelOrder(orderId, cancelReason, cancelNote) {
  try {
    // 1. 验证订单状态
    const order = await getOrderById(orderId)
    if (!order.canCancel()) {
      throw new Error('订单当前状态不允许取消')
    }
    
    // 2. 更新订单状态和取消时间
    const cancelTime = new Date().toISOString()
    await updateOrder(orderId, {
      status: 'CANCELLED',
      cancelTime: cancelTime,  // 🆕 新增：记录取消时间
      cancelReason: cancelReason,
      cancelNote: cancelNote,
      updateTime: cancelTime
    })
    
    // 3. 处理退款逻辑
    const refundResult = await processRefund(orderId)
    
    // 4. 发送取消通知
    await sendCancelNotification(orderId, cancelTime)
    
    return {
      orderId,
      status: 'CANCELLED',
      cancelTime,
      refundInfo: refundResult
    }
  } catch (error) {
    throw new Error(`订单取消失败: ${error.message}`)
  }
}
```

### 3. 数据库表结构变更

#### 3.1 订单表(orders)字段新增
```sql
-- 新增取消时间字段
ALTER TABLE orders 
ADD COLUMN cancel_time TIMESTAMP NULL 
COMMENT '订单取消时间';

-- 新增取消原因字段
ALTER TABLE orders 
ADD COLUMN cancel_reason VARCHAR(100) NULL 
COMMENT '订单取消原因';

-- 新增取消备注字段
ALTER TABLE orders 
ADD COLUMN cancel_note TEXT NULL 
COMMENT '订单取消备注';

-- 添加索引优化查询性能
CREATE INDEX idx_orders_cancel_time ON orders(cancel_time);
CREATE INDEX idx_orders_status_cancel_time ON orders(status, cancel_time);
CREATE INDEX idx_orders_cancel_reason ON orders(cancel_reason);
```

#### 3.2 字段说明
| 字段名 | 类型 | 长度 | 默认值 | 说明 |
|--------|------|------|--------|------|
| `cancel_time` | TIMESTAMP | - | NULL | 订单取消时间 |
| `cancel_reason` | VARCHAR | 100 | NULL | 取消原因 |
| `cancel_note` | TEXT | - | NULL | 取消备注 |

#### 3.3 数据迁移脚本
```sql
-- 历史数据迁移：将已取消订单的更新时间作为取消时间
UPDATE orders 
SET cancel_time = update_time,
    cancel_reason = '历史数据迁移'
WHERE status = 'CANCELLED' 
  AND cancel_time IS NULL;

-- 验证迁移结果
SELECT 
  COUNT(*) as total_cancelled,
  COUNT(cancel_time) as with_cancel_time,
  COUNT(*) - COUNT(cancel_time) as missing_cancel_time
FROM orders 
WHERE status = 'CANCELLED';
```

### 4. 数据验证规则

#### 4.1 前端验证
```javascript
// 取消时间字段验证
const validateOrderData = (orderData) => {
  const errors = []
  
  // 验证取消时间与订单状态的一致性
  if (orderData.status === 'CANCELLED') {
    if (!orderData.cancelTime) {
      errors.push('已取消订单必须包含取消时间')
    } else if (!isValidISODate(orderData.cancelTime)) {
      errors.push('取消时间格式无效')
    }
  } else if (orderData.cancelTime) {
    errors.push('非取消订单不应包含取消时间')
  }
  
  // 验证取消时间的合理性
  if (orderData.cancelTime) {
    const cancelTime = new Date(orderData.cancelTime)
    const orderTime = new Date(orderData.orderTime)
    
    if (cancelTime < orderTime) {
      errors.push('取消时间不能早于下单时间')
    }
    
    if (cancelTime > new Date()) {
      errors.push('取消时间不能晚于当前时间')
    }
  }
  
  return errors
}

// ISO日期格式验证
const isValidISODate = (dateString) => {
  const isoRegex = /^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}(\.\d{3})?Z$/
  return isoRegex.test(dateString) && !isNaN(Date.parse(dateString))
}
```

#### 4.2 后端验证
```javascript
// 服务端数据验证
const orderValidationSchema = {
  cancelTime: {
    type: 'string',
    format: 'date-time',
    nullable: true,
    custom: (value, order) => {
      // 状态一致性验证
      if (order.status === 'CANCELLED' && !value) {
        throw new ValidationError('已取消订单必须包含取消时间')
      }
      
      if (order.status !== 'CANCELLED' && value) {
        throw new ValidationError('非取消订单不应包含取消时间')
      }
      
      // 时间合理性验证
      if (value) {
        const cancelTime = new Date(value)
        const orderTime = new Date(order.orderTime)
        
        if (cancelTime < orderTime) {
          throw new ValidationError('取消时间不能早于下单时间')
        }
        
        if (cancelTime > new Date()) {
          throw new ValidationError('取消时间不能晚于当前时间')
        }
      }
    }
  }
}
```

### 5. 接口测试用例

#### 5.1 正常场景测试
```javascript
describe('订单详情接口 - 取消时间字段', () => {
  test('已取消订单应返回取消时间', async () => {
    const response = await request(app)
      .get('/api/h5/order/detail')
      .query({ orderId: '1705023' })
      .set('Authorization', 'Bearer valid_token')
      .expect(200)
    
    expect(response.body.data.status).toBe('CANCELLED')
    expect(response.body.data.cancelTime).toBeDefined()
    expect(response.body.data.cancelTime).toMatch(/^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}/)
  })
  
  test('非取消订单不应返回取消时间', async () => {
    const response = await request(app)
      .get('/api/h5/order/detail')
      .query({ orderId: '1705024' })
      .set('Authorization', 'Bearer valid_token')
      .expect(200)
    
    expect(response.body.data.status).not.toBe('CANCELLED')
    expect(response.body.data.cancelTime).toBeUndefined()
  })
})
```

#### 5.2 异常场景测试
```javascript
describe('订单详情接口 - 异常场景', () => {
  test('订单不存在时返回404', async () => {
    const response = await request(app)
      .get('/api/h5/order/detail')
      .query({ orderId: 'non_existent' })
      .set('Authorization', 'Bearer valid_token')
      .expect(404)
    
    expect(response.body.message).toBe('订单不存在')
  })
  
  test('未授权访问返回401', async () => {
    await request(app)
      .get('/api/h5/order/detail')
      .query({ orderId: '1705023' })
      .expect(401)
  })
})
```

#### 5.3 性能测试
```javascript
describe('订单详情接口 - 性能测试', () => {
  test('接口响应时间应小于500ms', async () => {
    const startTime = Date.now()
    
    await request(app)
      .get('/api/h5/order/detail')
      .query({ orderId: '1705023' })
      .set('Authorization', 'Bearer valid_token')
      .expect(200)
    
    const responseTime = Date.now() - startTime
    expect(responseTime).toBeLessThan(500)
  })
})
```

### 6. 接口文档示例

#### 6.1 Swagger/OpenAPI规范
```yaml
paths:
  /api/h5/order/detail:
    get:
      summary: 获取订单详情
      tags:
        - 订单管理
      parameters:
        - name: orderId
          in: query
          required: true
          schema:
            type: string
          description: 订单ID
      responses:
        '200':
          description: 成功获取订单详情
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    example: 200
                  message:
                    type: string
                    example: "success"
                  data:
                    $ref: '#/components/schemas/OrderDetail'

components:
  schemas:
    OrderDetail:
      type: object
      properties:
        id:
          type: integer
          description: 订单ID
        orderNo:
          type: string
          description: 订单号
        status:
          type: string
          enum: [CONFIRMED, DISPATCHED, PICKED_UP, RETURNED, CANCELLED]
          description: 订单状态
        cancelTime:
          type: string
          format: date-time
          nullable: true
          description: 订单取消时间（仅取消订单返回）
          example: "2025-05-19T11:45:00Z"
```

## 📊 接口变更影响分析

### 7.1 向后兼容性
- **完全兼容**: 新增字段不影响现有客户端
- **渐进升级**: 客户端可选择性使用新字段
- **版本控制**: 通过API版本号管理变更

### 7.2 性能影响
- **查询性能**: 新增字段对查询性能影响极小
- **网络传输**: 响应数据增加约20字节
- **数据库负载**: 新增索引提升查询效率

### 7.3 安全考虑
- **数据敏感性**: 取消时间为非敏感信息
- **权限控制**: 沿用现有订单权限验证机制
- **数据完整性**: 通过约束确保数据一致性

## ⚠️ 注意事项

### 8.1 开发注意事项
- 确保取消时间在订单取消时正确记录
- 处理历史数据的取消时间缺失问题
- 保证时区转换的准确性

### 8.2 测试注意事项
- 验证不同订单状态下的字段返回逻辑
- 测试时间格式的正确性和一致性
- 检查接口性能和响应时间

### 8.3 部署注意事项
- 先执行数据库变更，再部署应用代码
- 执行历史数据迁移脚本
- 监控接口性能和错误率
